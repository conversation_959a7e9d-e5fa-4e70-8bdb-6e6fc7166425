import DateTimePicker, { DateTimePickerEvent } from '@react-native-community/datetimepicker';
import React, { useEffect, useState } from 'react';
import {
  Modal,
  Platform,
  Pressable,
  ScrollView,
  StyleSheet,
  Switch,
  Text,
  TextInput,
  View,
} from 'react-native';
import { NewTaskForm, Task, TaskType } from '../types';

interface AddTaskModalProps {
  visible: boolean;
  onClose: () => void;
  onSaveTask: (task: NewTaskForm) => void;
  editingTask: Task | null;
}

const priorityMap = ['low', 'medium', 'high'];

export function AddTaskModal({ visible, onClose, onSaveTask, editingTask }: AddTaskModalProps) {
  // Common fields
  const [title, setTitle] = useState('');
  const [taskType, setTaskType] = useState<TaskType>('task');
  const [notes, setNotes] = useState('');
  const [priority, setPriority] = useState(1); // 0: low, 1: medium, 2: high
  const [isRecurring, setIsRecurring] = useState(false);

  // Date/Time picker state
  const [pickerState, setPickerState] = useState<{
    show: boolean;
    mode: 'date' | 'time';
    target: 'dueDate' | 'completeBy' | 'startTime' | 'endTime' | null;
  }>({ show: false, mode: 'date', target: null });

  // Type-specific fields
  const [dueDate, setDueDate] = useState(new Date());
  const [completeBy, setCompleteBy] = useState(new Date());
  const [duration, setDuration] = useState('60'); // in minutes
  const [flexibility, setFlexibility] = useState(5);
  const [startTime, setStartTime] = useState(new Date());
  const [endTime, setEndTime] = useState(new Date(Date.now() + 60 * 60 * 1000));

  useEffect(() => {
    if (visible) {
      if (editingTask) {
        // Populate form for editing
        setTitle(editingTask.title || '');
        setTaskType(editingTask.task_type || 'task');
        setNotes(editingTask.notes || '');
        setPriority(priorityMap.indexOf(editingTask.priority || 'medium'));
        setIsRecurring(editingTask.is_recurring || false);

        // Dates need to be parsed from string to Date object
        setDueDate(editingTask.due_date ? new Date(editingTask.due_date) : new Date());
        setCompleteBy(editingTask.complete_by ? new Date(editingTask.complete_by) : new Date());
        setDuration(String(editingTask.duration_minutes || 60));
        setFlexibility(editingTask.flexibility || 5);
        setStartTime(editingTask.start_time ? new Date(editingTask.start_time) : new Date());
        setEndTime(editingTask.end_time ? new Date(editingTask.end_time) : new Date());
      } else {
        // Reset form for new task
        setTitle('');
        setTaskType('task');
        setNotes('');
        setPriority(1);
        setIsRecurring(false);
        setDueDate(new Date());
        setCompleteBy(new Date());
        setDuration('60');
        setFlexibility(5);
        setStartTime(new Date());
        setEndTime(new Date(Date.now() + 60 * 60 * 1000));
      }
    }
  }, [visible, editingTask]);

  const handleSave = () => {
    if (!title) return; // Basic validation

    const baseTask: Partial<NewTaskForm> = {
      title,
      taskType,
      notes,
      priority: priorityMap[priority] as 'low' | 'medium' | 'high',
      isRecurring,
    };

    let finalTask: NewTaskForm;

    switch (taskType) {
      case 'item':
        finalTask = { ...baseTask, taskType: 'item', dueDate: dueDate };
        break;
      case 'task':
        finalTask = {
          ...baseTask,
          taskType: 'task',
          completeBy: completeBy,
          duration: parseInt(duration, 10) || 60,
          flexibility,
        };
        break;
      case 'event':
        finalTask = { ...baseTask, taskType: 'event', startTime: startTime, endTime: endTime };
        break;
    }

    onSaveTask(finalTask);
    onClose();
  };

  const onDateChange = (event: DateTimePickerEvent, selectedDate?: Date) => {
    const currentDate = selectedDate || new Date();
    setPickerState({ ...pickerState, show: Platform.OS === 'ios' });

    if (event.type === 'set') {
        switch (pickerState.target) {
            case 'dueDate': setDueDate(currentDate); break;
            case 'completeBy': setCompleteBy(currentDate); break;
            case 'startTime': setStartTime(currentDate); break;
            case 'endTime': setEndTime(currentDate); break;
        }
    }
  };

  const showDateTimePicker = (target: 'dueDate' | 'completeBy' | 'startTime' | 'endTime', mode: 'date' | 'time') => {
    setPickerState({ show: true, mode, target });
  };

  const renderTaskTypeSelector = () => (
    <View style={styles.selectorContainer}>
      {(['item', 'task', 'event'] as TaskType[]).map((type) => (
        <Pressable
          key={type}
          style={[styles.selectorButton, taskType === type && styles.selectorButtonActive]}
          onPress={() => setTaskType(type)}>
          <Text style={[styles.selectorText, taskType === type && styles.selectorTextActive]}>
            {type.charAt(0).toUpperCase() + type.slice(1)}
          </Text>
        </Pressable>
      ))}
    </View>
  );

  const renderPrioritySelector = () => (
    <View style={styles.selectorContainer}>
      {(['low', 'medium', 'high'] as const).map((p, index) => (
        <Pressable
          key={p}
          style={[
            styles.selectorButton,
            priority === index && styles.selectorButtonActive,
          ]}
          onPress={() => setPriority(index)}>
          <Text
            style={[
              styles.selectorText,
              priority === index && styles.selectorTextActive,
            ]}>
            {p.charAt(0).toUpperCase() + p.slice(1)}
          </Text>
        </Pressable>
      ))}
    </View>
  );

  const renderFieldsForTaskType = () => {
    switch (taskType) {
      case 'item':
        return (
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Due Date</Text>
            <Pressable onPress={() => showDateTimePicker('dueDate', 'date')}>
              <Text style={styles.dateText}>{dueDate.toLocaleDateString()}</Text>
            </Pressable>
          </View>
        );
      case 'task':
        return (
          <>
            <View style={styles.inputGroup}>
              <Text style={styles.label}>Complete By</Text>
              <Pressable onPress={() => showDateTimePicker('completeBy', 'date')}>
                <Text style={styles.dateText}>{completeBy.toLocaleDateString()}</Text>
              </Pressable>
            </View>
            <View style={styles.inputGroup}>
              <Text style={styles.label}>Duration (minutes)</Text>
              <TextInput
                style={styles.textInput}
                value={duration}
                onChangeText={setDuration}
                keyboardType="numeric"
                placeholder="e.g., 60"
              />
            </View>
            <View style={styles.inputGroup}>
              <Text style={styles.label}>Flexibility (1-10)</Text>
              <TextInput
                style={styles.textInput}
                value={String(flexibility)}
                onChangeText={(text) => setFlexibility(parseInt(text, 10) || 1)}
                keyboardType="numeric"
                placeholder="e.g., 5"
              />
            </View>
          </>
        );
      case 'event':
        return (
          <>
            <View style={styles.inputGroup}>
              <Text style={styles.label}>Start Time</Text>
              <Pressable onPress={() => showDateTimePicker('startTime', 'date')}>
                <Text style={styles.dateText}>{startTime.toLocaleString()}</Text>
              </Pressable>
            </View>
            <View style={styles.inputGroup}>
              <Text style={styles.label}>End Time</Text>
              <Pressable onPress={() => showDateTimePicker('endTime', 'date')}>
                <Text style={styles.dateText}>{endTime.toLocaleString()}</Text>
              </Pressable>
            </View>
          </>
        );
      default:
        return null;
    }
  };

  return (
    <Modal visible={visible} animationType="slide" onRequestClose={onClose}>
      <View style={styles.modalContainer}>
        <View style={styles.header}>
          <Pressable onPress={onClose}><Text style={styles.headerButton}>Cancel</Text></Pressable>
          <Text style={styles.headerTitle}>{editingTask ? 'Edit Task' : 'Add New'}</Text>
          <Pressable onPress={handleSave}><Text style={styles.headerButton}>Save</Text></Pressable>
        </View>

        <ScrollView style={styles.form} contentContainerStyle={{ paddingBottom: 100 }}>
          {renderTaskTypeSelector()}
          <TextInput
            style={[styles.textInput, styles.titleInput]}
            placeholder="Title *"
            value={title}
            onChangeText={setTitle}
          />
          {renderFieldsForTaskType()}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Priority</Text>
            {renderPrioritySelector()}
          </View>
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Recurring Daily</Text>
            <Switch value={isRecurring} onValueChange={setIsRecurring} />
          </View>
          <TextInput
            style={[styles.textInput, styles.notesInput]}
            placeholder="Notes"
            value={notes}
            onChangeText={setNotes}
            multiline
          />
        </ScrollView>

        {pickerState.show && (
          <DateTimePicker
            value={
                pickerState.target === 'dueDate' ? dueDate :
                pickerState.target === 'completeBy' ? completeBy :
                pickerState.target === 'startTime' ? startTime : endTime
            }
            mode={pickerState.mode}
            display="default"
            onChange={onDateChange}
          />
        )}
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  modalContainer: { flex: 1, backgroundColor: '#f0f2f5', paddingTop: 50 },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#dee2e6',
    backgroundColor: 'white',
  },
  headerTitle: { fontSize: 18, fontWeight: 'bold' },
  headerButton: { fontSize: 16, color: '#007aff' },
  form: { padding: 16 },
  selectorContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 20,
    backgroundColor: '#e9ecef',
    borderRadius: 8,
  },
  selectorButton: { flex: 1, paddingVertical: 12, alignItems: 'center' },
  selectorButtonActive: { backgroundColor: '#007aff', borderRadius: 8 },
  selectorText: { fontSize: 16, color: '#495057' },
  selectorTextActive: { color: 'white', fontWeight: 'bold' },
  inputGroup: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
  },
  label: { fontSize: 16, color: '#495057', marginBottom: 8 },
  textInput: {
    fontSize: 16,
    padding: 12,
    borderWidth: 1,
    borderColor: '#ced4da',
    borderRadius: 8,
    backgroundColor: '#f8f9fa',
  },
  titleInput: { marginBottom: 16 },
  notesInput: { height: 100, textAlignVertical: 'top' },
  dateText: { fontSize: 16, paddingVertical: 12, color: '#007aff' },
});